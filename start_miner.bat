@echo off
echo ================================================
echo    Bitcoin Miner Pro - Whatsminer M61s 224TH/s
echo ================================================
echo.
echo Starting Bitcoin Miner Pro...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "bitcoin_miner_gui.py" (
    echo Error: bitcoin_miner_gui.py not found
    echo Please make sure all files are in the same directory
    pause
    exit /b 1
)

REM Install requirements if needed
if exist "requirements.txt" (
    echo Installing/checking requirements...
    pip install -r requirements.txt >nul 2>&1
)

REM Start the application
echo Launching Bitcoin Miner Pro GUI...
python bitcoin_miner_gui.py

echo.
echo Bitcoin Miner Pro has been closed.
pause
