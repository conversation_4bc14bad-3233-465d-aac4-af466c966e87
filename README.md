# Bitcoin Miner Pro - Whatsminer M61s Simulator

یک برنامه گرافیکی حرفه‌ای برای ماینینگ بیتکوین که خود را به عنوان یک ماینر Whatsminer M61s با قدرت 224 TH/s معرفی می‌کند.

## ویژگی‌های کلیدی

- **شبیه‌سازی ماینر حرفه‌ای**: خود را به عنوان Whatsminer M61s 224TH/s معرفی می‌کند
- **پشتیبانی از چندین استخر**: امکان اتصال همزمان به چندین استخر ماینینگ
- **رابط کاربری گرافیکی**: رابط کاربری کامل و حرفه‌ای با tkinter
- **پروتکل Stratum**: پیاده‌سازی کامل پروتکل Stratum برای اتصال به استخرها
- **آمار دقیق**: نمایش آمار دقیق ماینینگ شامل hashrate، shares و uptime
- **استخرهای از پیش تعریف شده**: شامل AntPool، F2Pool و سایر استخرهای معروف

## استخرهای پشتیبانی شده

### AntPool
- `stratum+tcp://ss.antpool.com:3333`
- `stratum+tcp://ss.antpool.com:443`
- `stratum+tcp://ss.antpool.com:25`

### F2Pool
- **Universal**: `stratum+tcp://btc.f2pool.com:1314`
- **SSL**: `stratum+ssl://btcssl.f2pool.com:1300`
- **Asia**: `stratum+tcp://btc-asia.f2pool.com:1314`
- **North America**: `stratum+tcp://btc-na.f2pool.com:1314`
- **Europe**: `stratum+tcp://btc-euro.f2pool.com:1314`
- **Africa**: `stratum+tcp://btc-africa.f2pool.com:1314`
- **Latin America**: `stratum+tcp://btc-latin.f2pool.com:1314`

## نصب و راه‌اندازی

### پیش‌نیازها
- Python 3.7 یا بالاتر
- کتابخانه‌های مورد نیاز (در فایل requirements.txt)

### مراحل نصب

1. **کلون کردن پروژه**:
```bash
git clone <repository-url>
cd bitcoin-miner-pro
```

2. **نصب وابستگی‌ها**:
```bash
pip install -r requirements.txt
```

3. **اجرای برنامه**:
```bash
python bitcoin_miner_gui.py
```

## راهنمای استفاده

### 1. تنظیم استخر ماینینگ

1. به تب "Pool Configuration" بروید
2. از لیست استخرهای از پیش تعریف شده یکی را انتخاب کنید یا URL دستی وارد کنید
3. نام کاربری و رمز عبور خود را وارد کنید
4. نام worker را تنظیم کنید (پیش‌فرض: Whatsminer_M61s_001)
5. روی "Add Pool" کلیک کنید

### 2. شروع ماینینگ

1. به تب "Mining Control" بروید
2. روی "Start Mining" کلیک کنید
3. وضعیت اتصال و ماینینگ را در قسمت "Current Status" مشاهده کنید

### 3. مشاهده آمار

1. به تب "Statistics" بروید
2. آمار کلی و جزئیات هر استخر را مشاهده کنید
3. آمار شامل:
   - Total Hashrate
   - Connected Pools
   - Shares Accepted/Rejected
   - Uptime
   - Rejection Rate

### 4. مشاهده لاگ‌ها

1. به تب "Logs" بروید
2. تمام فعالیت‌های ماینینگ را مشاهده کنید
3. امکان ذخیره لاگ‌ها در فایل

## مشخصات فنی

### شبیه‌سازی Whatsminer M61s
- **مدل**: Whatsminer M61s
- **Hashrate**: 224 TH/s (224,000,000,000,000 H/s)
- **مصرف برق**: 3348 وات
- **بازدهی**: 14.9 J/TH

### پروتکل‌های پشتیبانی شده
- Stratum over TCP
- Stratum over SSL (محدود)

### ویژگی‌های امنیتی
- اتصال امن به استخرها
- مدیریت خطاها و reconnection
- لاگ کامل فعالیت‌ها

## ساختار فایل‌ها

```
bitcoin-miner-pro/
├── bitcoin_miner_gui.py      # رابط کاربری اصلی
├── mining_engine.py          # موتور ماینینگ
├── stratum_client.py         # کلاینت پروتکل Stratum
├── requirements.txt          # وابستگی‌های پایتون
└── README.md                # این فایل
```

## توضیحات فنی

### کلاس StratumClient
- مدیریت اتصال به استخرهای ماینینگ
- پیاده‌سازی پروتکل Stratum
- مدیریت job ها و submission های share

### کلاس MiningEngine
- مدیریت چندین استخر همزمان
- محاسبه آمار کلی
- کنترل شروع/توقف ماینینگ

### کلاس BitcoinMinerGUI
- رابط کاربری گرافیکی
- مدیریت تب‌ها و نمایش اطلاعات
- کنترل‌های کاربری

## نکات مهم

⚠️ **هشدار**: این برنامه یک شبیه‌ساز است و ماینینگ واقعی انجام نمی‌دهد. برای استفاده تجاری، نیاز به پیاده‌سازی الگوریتم‌های ماینینگ واقعی دارید.

🔧 **تنظیمات**: برای بهترین عملکرد، تنظیمات شبکه و فایروال خود را بررسی کنید.

📊 **آمار**: آمار نمایش داده شده شبیه‌سازی شده است و بر اساس مشخصات Whatsminer M61s محاسبه می‌شود.

## پشتیبانی

برای گزارش مشکلات یا پیشنهادات، لطفاً issue ایجاد کنید.

## مجوز

این پروژه تحت مجوز MIT منتشر شده است.
