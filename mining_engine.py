import hashlib
import struct
import time
import threading
from datetime import datetime
from stratum_client import StratumClient

class MiningEngine:
    def __init__(self):
        self.clients = []
        self.active_pools = []
        self.total_hashrate = 0
        self.total_shares_accepted = 0
        self.total_shares_rejected = 0
        self.start_time = None
        self.mining_active = False
        self.stats_callbacks = []
        
        # Miner identification (simulating Whatsminer M61s)
        self.miner_model = "Whatsminer M61s"
        self.miner_hashrate = 224000000000000  # 224 TH/s
        self.miner_power = 3348  # Watts
        self.miner_efficiency = 14.9  # J/TH
        
    def add_pool(self, host, port, username, password, worker_name=None):
        """Add a mining pool to the engine"""
        if not worker_name:
            worker_name = f"Whatsminer_M61s_{len(self.clients)+1}"
            
        try:
            # Parse stratum URL if provided
            if host.startswith('stratum+tcp://'):
                host = host.replace('stratum+tcp://', '')
            elif host.startswith('stratum+ssl://'):
                host = host.replace('stratum+ssl://', '')
                
            if ':' in host:
                host, port_str = host.split(':')
                port = int(port_str)
                
            client = StratumClient(host, port, username, password, worker_name)
            self.clients.append(client)
            
            pool_info = {
                'host': host,
                'port': port,
                'username': username,
                'worker_name': worker_name,
                'client': client
            }
            self.active_pools.append(pool_info)
            
            return True
        except Exception as e:
            print(f"Error adding pool: {e}")
            return False
    
    def remove_pool(self, index):
        """Remove a pool from the engine"""
        if 0 <= index < len(self.clients):
            client = self.clients[index]
            if client.connected:
                client.disconnect()
            self.clients.pop(index)
            self.active_pools.pop(index)
            return True
        return False
    
    def start_mining(self):
        """Start mining on all configured pools"""
        if self.mining_active:
            return False
            
        if not self.clients:
            print("No pools configured!")
            return False
            
        self.mining_active = True
        self.start_time = datetime.now()
        
        # Connect to all pools
        connected_count = 0
        for i, client in enumerate(self.clients):
            print(f"Connecting to pool {i+1}: {client.host}:{client.port}")
            if client.connect():
                connected_count += 1
                print(f"Connected to {client.host}:{client.port}")
            else:
                print(f"Failed to connect to {client.host}:{client.port}")
        
        if connected_count == 0:
            print("Failed to connect to any pools!")
            self.mining_active = False
            return False
            
        print(f"Mining started on {connected_count} pools")
        
        # Start stats update thread
        stats_thread = threading.Thread(target=self._update_stats)
        stats_thread.daemon = True
        stats_thread.start()
        
        return True
    
    def stop_mining(self):
        """Stop mining on all pools"""
        self.mining_active = False
        
        for client in self.clients:
            if client.connected:
                client.disconnect()
                
        print("Mining stopped")
    
    def _update_stats(self):
        """Update mining statistics"""
        while self.mining_active:
            try:
                self._calculate_total_stats()
                
                # Notify callbacks
                for callback in self.stats_callbacks:
                    try:
                        callback(self.get_total_stats())
                    except:
                        pass
                        
                time.sleep(1)  # Update every second
            except Exception as e:
                print(f"Stats update error: {e}")
                break
    
    def _calculate_total_stats(self):
        """Calculate total statistics from all pools"""
        self.total_hashrate = 0
        self.total_shares_accepted = 0
        self.total_shares_rejected = 0
        
        for client in self.clients:
            if client.connected and client.authorized:
                stats = client.get_stats()
                self.total_hashrate += stats['hashrate']
                self.total_shares_accepted += stats['shares_accepted']
                self.total_shares_rejected += stats['shares_rejected']
    
    def get_total_stats(self):
        """Get total mining statistics"""
        uptime = 0
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
            
        connected_pools = sum(1 for client in self.clients if client.connected)
        
        # Calculate efficiency metrics
        avg_share_time = 0
        if self.total_shares_accepted > 0 and uptime > 0:
            avg_share_time = uptime / self.total_shares_accepted
            
        rejection_rate = 0
        if self.total_shares_accepted + self.total_shares_rejected > 0:
            rejection_rate = (self.total_shares_rejected / 
                            (self.total_shares_accepted + self.total_shares_rejected)) * 100
        
        return {
            'mining_active': self.mining_active,
            'connected_pools': connected_pools,
            'total_pools': len(self.clients),
            'total_hashrate': self.total_hashrate,
            'total_shares_accepted': self.total_shares_accepted,
            'total_shares_rejected': self.total_shares_rejected,
            'uptime': uptime,
            'avg_share_time': avg_share_time,
            'rejection_rate': rejection_rate,
            'miner_model': self.miner_model,
            'miner_power': self.miner_power,
            'miner_efficiency': self.miner_efficiency
        }
    
    def get_pool_stats(self):
        """Get individual pool statistics"""
        pool_stats = []
        for i, client in enumerate(self.clients):
            stats = client.get_stats()
            pool_info = self.active_pools[i]
            
            pool_stat = {
                'index': i,
                'host': pool_info['host'],
                'port': pool_info['port'],
                'worker_name': pool_info['worker_name'],
                'connected': stats['connected'],
                'authorized': stats.get('authorized', False),
                'hashrate': stats['hashrate'],
                'shares_accepted': stats['shares_accepted'],
                'shares_rejected': stats['shares_rejected'],
                'uptime': stats['uptime'],
                'difficulty': stats.get('difficulty', 0),
                'job_id': stats.get('job_id', 'None')
            }
            pool_stats.append(pool_stat)
            
        return pool_stats
    
    def add_stats_callback(self, callback):
        """Add a callback function for stats updates"""
        self.stats_callbacks.append(callback)
    
    def remove_stats_callback(self, callback):
        """Remove a stats callback"""
        if callback in self.stats_callbacks:
            self.stats_callbacks.remove(callback)
    
    def format_hashrate(self, hashrate):
        """Format hashrate for display"""
        if hashrate >= 1e15:
            return f"{hashrate/1e15:.2f} PH/s"
        elif hashrate >= 1e12:
            return f"{hashrate/1e12:.2f} TH/s"
        elif hashrate >= 1e9:
            return f"{hashrate/1e9:.2f} GH/s"
        elif hashrate >= 1e6:
            return f"{hashrate/1e6:.2f} MH/s"
        elif hashrate >= 1e3:
            return f"{hashrate/1e3:.2f} KH/s"
        else:
            return f"{hashrate:.2f} H/s"
    
    def format_uptime(self, seconds):
        """Format uptime for display"""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            return f"{int(seconds//60)}m {int(seconds%60)}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"
