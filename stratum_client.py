import socket
import json
import threading
import time
import hashlib
import struct
from datetime import datetime

class StratumClient:
    def __init__(self, host, port, username, password, worker_name="Whatsminer_M61s_224Th"):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.worker_name = worker_name
        self.socket = None
        self.connected = False
        self.subscribed = False
        self.authorized = False
        self.job = None
        self.extranonce1 = None
        self.extranonce2_size = None
        self.difficulty = 1
        self.target = None
        self.message_id = 1
        self.callbacks = {}
        self.mining_thread = None
        self.stop_mining = False
        self.hashrate = 224000000000000  # 224 TH/s in H/s
        self.shares_accepted = 0
        self.shares_rejected = 0
        self.start_time = None
        
    def connect(self):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)
            self.socket.connect((self.host, self.port))
            self.connected = True
            self.start_time = datetime.now()
            
            # Start listening thread
            listen_thread = threading.Thread(target=self._listen)
            listen_thread.daemon = True
            listen_thread.start()
            
            # Subscribe to mining
            self._subscribe()
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
    
    def disconnect(self):
        self.stop_mining = True
        self.connected = False
        if self.socket:
            self.socket.close()
    
    def _send_message(self, method, params=None, callback=None):
        if not self.connected:
            return False
            
        message = {
            "id": self.message_id,
            "method": method,
            "params": params or []
        }
        
        if callback:
            self.callbacks[self.message_id] = callback
            
        try:
            data = json.dumps(message) + "\n"
            self.socket.send(data.encode())
            self.message_id += 1
            return True
        except Exception as e:
            print(f"Send failed: {e}")
            return False
    
    def _listen(self):
        buffer = ""
        while self.connected:
            try:
                data = self.socket.recv(4096).decode()
                if not data:
                    break
                    
                buffer += data
                while "\n" in buffer:
                    line, buffer = buffer.split("\n", 1)
                    if line.strip():
                        self._handle_message(line.strip())
                        
            except Exception as e:
                print(f"Listen error: {e}")
                break
                
        self.connected = False
    
    def _handle_message(self, message):
        try:
            data = json.loads(message)
            
            # Handle responses
            if "id" in data and data["id"] in self.callbacks:
                callback = self.callbacks.pop(data["id"])
                callback(data)
            
            # Handle notifications
            elif "method" in data:
                method = data["method"]
                params = data.get("params", [])
                
                if method == "mining.notify":
                    self._handle_job(params)
                elif method == "mining.set_difficulty":
                    self._handle_difficulty(params)
                elif method == "mining.set_extranonce":
                    self._handle_extranonce(params)
                    
        except Exception as e:
            print(f"Message handling error: {e}")
    
    def _subscribe(self):
        def on_subscribe(response):
            if "result" in response and response["result"]:
                result = response["result"]
                if len(result) >= 2:
                    self.extranonce1 = result[1]
                    self.extranonce2_size = result[2] if len(result) > 2 else 4
                    self.subscribed = True
                    self._authorize()
        
        self._send_message("mining.subscribe", 
                          [f"BitcoinMiner/{self.worker_name}", None, self.host, self.port],
                          on_subscribe)
    
    def _authorize(self):
        def on_authorize(response):
            if "result" in response and response["result"]:
                self.authorized = True
                print(f"Authorized as {self.username}.{self.worker_name}")
                self._start_mining()
            else:
                print("Authorization failed")
        
        self._send_message("mining.authorize", 
                          [f"{self.username}.{self.worker_name}", self.password],
                          on_authorize)
    
    def _handle_job(self, params):
        if len(params) >= 8:
            self.job = {
                'job_id': params[0],
                'prevhash': params[1],
                'coinb1': params[2],
                'coinb2': params[3],
                'merkle_branch': params[4],
                'version': params[5],
                'nbits': params[6],
                'ntime': params[7],
                'clean_jobs': params[8] if len(params) > 8 else False
            }
            print(f"New job received: {self.job['job_id']}")
    
    def _handle_difficulty(self, params):
        if params:
            self.difficulty = params[0]
            # Calculate target from difficulty
            self.target = 0x00000000FFFF0000000000000000000000000000000000000000000000000000 // self.difficulty
            print(f"Difficulty set to: {self.difficulty}")
    
    def _handle_extranonce(self, params):
        if len(params) >= 2:
            self.extranonce1 = params[0]
            self.extranonce2_size = params[1]
    
    def _start_mining(self):
        if not self.mining_thread or not self.mining_thread.is_alive():
            self.stop_mining = False
            self.mining_thread = threading.Thread(target=self._mine)
            self.mining_thread.daemon = True
            self.mining_thread.start()
    
    def _mine(self):
        nonce = 0
        extranonce2 = 0
        
        while not self.stop_mining and self.connected and self.job:
            try:
                # Simulate mining work
                time.sleep(0.1)  # Simulate work time
                
                # Create a fake share submission every few seconds
                if nonce % 50 == 0:  # Submit share every 5 seconds
                    self._submit_share(nonce, extranonce2)
                
                nonce += 1
                if nonce > 0xFFFFFFFF:
                    nonce = 0
                    extranonce2 += 1
                    
            except Exception as e:
                print(f"Mining error: {e}")
                break
    
    def _submit_share(self, nonce, extranonce2):
        if not self.job:
            return
            
        def on_share_response(response):
            if "result" in response and response["result"]:
                self.shares_accepted += 1
                print(f"Share accepted! Total: {self.shares_accepted}")
            else:
                self.shares_rejected += 1
                error = response.get("error", "Unknown error")
                print(f"Share rejected: {error}. Total rejected: {self.shares_rejected}")
        
        # Format extranonce2
        extranonce2_hex = format(extranonce2, f'0{self.extranonce2_size * 2}x')
        ntime = self.job['ntime']
        nonce_hex = format(nonce, '08x')
        
        self._send_message("mining.submit",
                          [f"{self.username}.{self.worker_name}",
                           self.job['job_id'],
                           extranonce2_hex,
                           ntime,
                           nonce_hex],
                          on_share_response)
    
    def get_stats(self):
        if not self.start_time:
            return {
                'connected': self.connected,
                'hashrate': 0,
                'shares_accepted': 0,
                'shares_rejected': 0,
                'uptime': 0
            }
            
        uptime = (datetime.now() - self.start_time).total_seconds()
        current_hashrate = self.hashrate if self.connected and self.authorized else 0
        
        return {
            'connected': self.connected,
            'authorized': self.authorized,
            'hashrate': current_hashrate,
            'shares_accepted': self.shares_accepted,
            'shares_rejected': self.shares_rejected,
            'uptime': uptime,
            'difficulty': self.difficulty,
            'job_id': self.job['job_id'] if self.job else None
        }
