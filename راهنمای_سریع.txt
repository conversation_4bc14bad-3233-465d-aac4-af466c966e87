🚀 راهنمای سریع Bitcoin Miner Pro 🚀

═══════════════════════════════════════════════════════════════

📋 مراحل راه‌اندازی:

1️⃣ اجرای برنامه:
   - روی فایل start_miner.bat دابل کلیک کنید
   - یا در command prompt بنویسید: python bitcoin_miner_gui.py

2️⃣ اضافه کردن استخر ماینینگ:
   - به تب "Pool Configuration" بروید
   - از لیست استخرهای آماده یکی انتخاب کنید یا URL دستی وارد کنید
   - نام کاربری و رمز عبور خود را وارد کنید
   - روی "Add Pool" کلیک کنید

3️⃣ شروع ماینینگ:
   - به تب "Mining Control" بروید
   - روی "Start Mining" کلیک کنید

═══════════════════════════════════════════════════════════════

🏊‍♂️ استخرهای پیشنهادی:

🔸 AntPool:
   - ss.antpool.com:3333
   - ss.antpool.com:443
   - ss.antpool.com:25

🔸 F2Pool:
   - btc.f2pool.com:1314 (جهانی)
   - btc-asia.f2pool.com:1314 (آسیا)
   - btc-na.f2pool.com:1314 (آمریکای شمالی)
   - btc-euro.f2pool.com:1314 (اروپا)

═══════════════════════════════════════════════════════════════

⚙️ مشخصات شبیه‌سازی شده:

🔹 مدل: Whatsminer M61s
🔹 قدرت: 224 TH/s (224,000,000,000,000 هش در ثانیه)
🔹 مصرف برق: 3348 وات
🔹 بازدهی: 14.9 ژول بر ترا هش

═══════════════════════════════════════════════════════════════

📊 نمایش آمار:

🔸 تب "Statistics": آمار کلی و جزئیات هر استخر
🔸 تب "Logs": لاگ کامل فعالیت‌ها
🔸 آمار شامل:
   - Total Hashrate (قدرت کل)
   - Connected Pools (استخرهای متصل)
   - Shares Accepted/Rejected (سهام پذیرفته/رد شده)
   - Uptime (مدت زمان فعالیت)
   - Rejection Rate (نرخ رد)

═══════════════════════════════════════════════════════════════

⚠️ نکات مهم:

🔴 این برنامه یک شبیه‌ساز است و ماینینگ واقعی انجام نمی‌دهد
🔴 برای ماینینگ واقعی نیاز به سخت‌افزار مخصوص دارید
🔴 اطلاعات کاربری معتبر برای اتصال به استخرها لازم است

🟢 برای تست می‌توانید از اطلاعات آزمایشی استفاده کنید
🟢 برنامه امن است و هیچ اطلاعاتی را ارسال نمی‌کند
🟢 تمام کدها قابل بررسی و متن‌باز هستند

═══════════════════════════════════════════════════════════════

🛠️ عیب‌یابی:

❌ خطای "Python not found":
   ✅ Python 3.7+ را نصب کنید

❌ خطای "Module not found":
   ✅ دستور pip install -r requirements.txt را اجرا کنید

❌ خطای اتصال به استخر:
   ✅ اتصال اینترنت و تنظیمات فایروال را بررسی کنید
   ✅ اطلاعات کاربری را بررسی کنید

❌ برنامه باز نمی‌شود:
   ✅ تمام فایل‌ها در یک پوشه باشند
   ✅ از command prompt اجرا کنید تا خطا را ببینید

═══════════════════════════════════════════════════════════════

📞 پشتیبانی:

برای سوالات و مشکلات:
- فایل‌های لاگ را بررسی کنید
- تست اتصال انجام دهید
- از تب "Logs" برای جزئیات استفاده کنید

═══════════════════════════════════════════════════════════════

موفق باشید! 🎯
