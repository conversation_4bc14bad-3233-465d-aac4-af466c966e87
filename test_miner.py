#!/usr/bin/env python3
"""
Test script for Bitcoin Miner Pro
تست اتصال به استخرهای مختلف
"""

import sys
import time
from mining_engine import MiningEngine

def test_mining_engine():
    """Test the mining engine functionality"""
    print("=== Bitcoin Miner Pro Test ===")
    print("Testing mining engine...")
    
    # Create mining engine
    engine = MiningEngine()
    
    # Test adding pools
    print("\n1. Testing pool addition...")
    
    # Test AntPool
    result1 = engine.add_pool("ss.antpool.com", 3333, "test_user", "x", "Whatsminer_M61s_Test1")
    print(f"AntPool added: {result1}")
    
    # Test F2Pool
    result2 = engine.add_pool("btc.f2pool.com", 1314, "test_user", "x", "Whatsminer_M61s_Test2")
    print(f"F2Pool added: {result2}")
    
    # Test with stratum URL
    result3 = engine.add_pool("stratum+tcp://btc-asia.f2pool.com:1314", 0, "test_user", "x", "Whatsminer_M61s_Test3")
    print(f"F2Pool Asia added: {result3}")
    
    # Display pool stats
    print("\n2. Pool configuration:")
    pool_stats = engine.get_pool_stats()
    for i, pool in enumerate(pool_stats):
        print(f"Pool {i+1}: {pool['host']}:{pool['port']} - Worker: {pool['worker_name']}")
    
    # Test stats
    print("\n3. Testing statistics...")
    stats = engine.get_total_stats()
    print(f"Total pools: {stats['total_pools']}")
    print(f"Miner model: {stats['miner_model']}")
    print(f"Hashrate: {engine.format_hashrate(stats['total_hashrate'])}")
    print(f"Power consumption: {stats['miner_power']}W")
    print(f"Efficiency: {stats['miner_efficiency']} J/TH")
    
    # Test connection (without actually connecting)
    print("\n4. Testing connection simulation...")
    print("Note: This is a simulation. Real connections require valid credentials.")
    
    # Test hashrate formatting
    print("\n5. Testing hashrate formatting...")
    test_rates = [1000, 1000000, 1000000000, 1000000000000, 224000000000000]
    for rate in test_rates:
        formatted = engine.format_hashrate(rate)
        print(f"{rate:,} H/s = {formatted}")
    
    # Test uptime formatting
    print("\n6. Testing uptime formatting...")
    test_times = [30, 90, 3600, 7200, 86400]
    for t in test_times:
        formatted = engine.format_uptime(t)
        print(f"{t} seconds = {formatted}")
    
    print("\n=== Test completed successfully! ===")
    print("\nTo run the GUI application, execute:")
    print("python bitcoin_miner_gui.py")

def test_stratum_client():
    """Test stratum client basic functionality"""
    print("\n=== Testing Stratum Client ===")
    
    from stratum_client import StratumClient
    
    # Create client (don't connect)
    client = StratumClient("ss.antpool.com", 3333, "test_user", "x", "Whatsminer_M61s_Test")
    
    print(f"Client created for {client.host}:{client.port}")
    print(f"Worker name: {client.worker_name}")
    print(f"Simulated hashrate: {client.hashrate:,} H/s")
    
    # Test stats without connection
    stats = client.get_stats()
    print(f"Initial stats: {stats}")
    
    print("Stratum client test completed!")

if __name__ == "__main__":
    try:
        test_mining_engine()
        test_stratum_client()
        
        print("\n" + "="*50)
        print("All tests passed! The Bitcoin Miner Pro is ready to use.")
        print("="*50)
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
