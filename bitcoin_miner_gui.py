import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
from mining_engine import MiningEngine

class BitcoinMinerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Bitcoin Miner Pro - Whatsminer M61s Simulator")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # Initialize mining engine
        self.mining_engine = MiningEngine()
        self.mining_engine.add_stats_callback(self.update_stats_display)
        
        # Style configuration
        self.setup_styles()
        
        # Create GUI components
        self.create_widgets()
        
        # Start GUI update thread
        self.start_gui_updates()
        
        # Predefined pools
        self.predefined_pools = [
            {"name": "AntPool", "urls": [
                "stratum+tcp://ss.antpool.com:3333",
                "stratum+tcp://ss.antpool.com:443",
                "stratum+tcp://ss.antpool.com:25"
            ]},
            {"name": "F2Pool Universal", "urls": [
                "stratum+tcp://btc.f2pool.com:1314",
                "stratum+ssl://btcssl.f2pool.com:1300"
            ]},
            {"name": "F2Pool Asia", "urls": [
                "stratum+tcp://btc-asia.f2pool.com:1314"
            ]},
            {"name": "F2Pool North America", "urls": [
                "stratum+tcp://btc-na.f2pool.com:1314"
            ]},
            {"name": "F2Pool Europe", "urls": [
                "stratum+tcp://btc-euro.f2pool.com:1314"
            ]},
            {"name": "F2Pool Africa", "urls": [
                "stratum+tcp://btc-africa.f2pool.com:1314"
            ]},
            {"name": "F2Pool Latin America", "urls": [
                "stratum+tcp://btc-latin.f2pool.com:1314"
            ]}
        ]
    
    def setup_styles(self):
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#2b2b2b', foreground='#ffffff')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#2b2b2b', foreground='#00ff00')
        style.configure('Info.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='#ffffff')
        style.configure('Status.TLabel', font=('Arial', 10, 'bold'), background='#2b2b2b', foreground='#ffff00')
        
    def create_widgets(self):
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="Bitcoin Miner Pro - Whatsminer M61s 224TH/s", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Mining tab
        self.create_mining_tab()
        
        # Pools tab
        self.create_pools_tab()
        
        # Statistics tab
        self.create_statistics_tab()
        
        # Logs tab
        self.create_logs_tab()
    
    def create_mining_tab(self):
        mining_frame = ttk.Frame(self.notebook)
        self.notebook.add(mining_frame, text="Mining Control")
        
        # Miner info section
        info_frame = ttk.LabelFrame(mining_frame, text="Miner Information", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(info_frame, text="Model: Whatsminer M61s", style='Info.TLabel').grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text="Hashrate: 224 TH/s", style='Info.TLabel').grid(row=0, column=1, sticky=tk.W, padx=(50, 0))
        ttk.Label(info_frame, text="Power: 3348W", style='Info.TLabel').grid(row=1, column=0, sticky=tk.W)
        ttk.Label(info_frame, text="Efficiency: 14.9 J/TH", style='Info.TLabel').grid(row=1, column=1, sticky=tk.W, padx=(50, 0))
        
        # Control section
        control_frame = ttk.LabelFrame(mining_frame, text="Mining Control", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.start_button = ttk.Button(control_frame, text="Start Mining", command=self.start_mining)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop Mining", command=self.stop_mining, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Status section
        status_frame = ttk.LabelFrame(mining_frame, text="Current Status", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.status_text = scrolledtext.ScrolledText(status_frame, height=15, bg='#1e1e1e', fg='#00ff00', 
                                                    font=('Courier', 10))
        self.status_text.pack(fill=tk.BOTH, expand=True)
        
        # Add initial status
        self.log_message("Bitcoin Miner Pro initialized")
        self.log_message("Ready to start mining...")
    
    def create_pools_tab(self):
        pools_frame = ttk.Frame(self.notebook)
        self.notebook.add(pools_frame, text="Pool Configuration")
        
        # Add pool section
        add_frame = ttk.LabelFrame(pools_frame, text="Add Mining Pool", padding=10)
        add_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Predefined pools dropdown
        ttk.Label(add_frame, text="Predefined Pools:").grid(row=0, column=0, sticky=tk.W)
        self.pool_var = tk.StringVar()
        pool_combo = ttk.Combobox(add_frame, textvariable=self.pool_var, width=30)
        pool_combo['values'] = [pool['name'] for pool in self.predefined_pools]
        pool_combo.grid(row=0, column=1, padx=5, sticky=tk.W)
        pool_combo.bind('<<ComboboxSelected>>', self.on_pool_selected)
        
        # Manual pool entry
        ttk.Label(add_frame, text="Pool URL:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.pool_url_var = tk.StringVar()
        self.pool_url_entry = ttk.Entry(add_frame, textvariable=self.pool_url_var, width=50)
        self.pool_url_entry.grid(row=1, column=1, columnspan=2, padx=5, sticky=tk.W)
        
        ttk.Label(add_frame, text="Username:").grid(row=2, column=0, sticky=tk.W)
        self.username_var = tk.StringVar()
        ttk.Entry(add_frame, textvariable=self.username_var, width=30).grid(row=2, column=1, padx=5, sticky=tk.W)
        
        ttk.Label(add_frame, text="Password:").grid(row=2, column=2, sticky=tk.W, padx=(20, 0))
        self.password_var = tk.StringVar(value="x")
        ttk.Entry(add_frame, textvariable=self.password_var, width=20, show="*").grid(row=2, column=3, padx=5, sticky=tk.W)
        
        ttk.Label(add_frame, text="Worker Name:").grid(row=3, column=0, sticky=tk.W)
        self.worker_var = tk.StringVar(value="Whatsminer_M61s_001")
        ttk.Entry(add_frame, textvariable=self.worker_var, width=30).grid(row=3, column=1, padx=5, sticky=tk.W)
        
        ttk.Button(add_frame, text="Add Pool", command=self.add_pool).grid(row=3, column=2, padx=20)
        
        # Pools list
        list_frame = ttk.LabelFrame(pools_frame, text="Configured Pools", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview for pools
        columns = ('Host', 'Port', 'Username', 'Worker', 'Status')
        self.pools_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.pools_tree.heading(col, text=col)
            self.pools_tree.column(col, width=150)
        
        self.pools_tree.pack(fill=tk.BOTH, expand=True)
        
        # Pool control buttons
        pool_buttons_frame = ttk.Frame(list_frame)
        pool_buttons_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(pool_buttons_frame, text="Remove Selected", command=self.remove_pool).pack(side=tk.LEFT, padx=5)
        ttk.Button(pool_buttons_frame, text="Test Connection", command=self.test_pool).pack(side=tk.LEFT, padx=5)
    
    def create_statistics_tab(self):
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="Statistics")
        
        # Overall stats
        overall_frame = ttk.LabelFrame(stats_frame, text="Overall Statistics", padding=10)
        overall_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.stats_labels = {}
        stats_info = [
            ('Total Hashrate:', 'total_hashrate'),
            ('Connected Pools:', 'connected_pools'),
            ('Shares Accepted:', 'shares_accepted'),
            ('Shares Rejected:', 'shares_rejected'),
            ('Uptime:', 'uptime'),
            ('Rejection Rate:', 'rejection_rate')
        ]
        
        for i, (label, key) in enumerate(stats_info):
            row = i // 2
            col = (i % 2) * 2
            ttk.Label(overall_frame, text=label, style='Info.TLabel').grid(row=row, column=col, sticky=tk.W, pady=2)
            self.stats_labels[key] = ttk.Label(overall_frame, text="0", style='Status.TLabel')
            self.stats_labels[key].grid(row=row, column=col+1, sticky=tk.W, padx=(10, 50), pady=2)
        
        # Pool-specific stats
        pool_stats_frame = ttk.LabelFrame(stats_frame, text="Pool Statistics", padding=10)
        pool_stats_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview for pool stats
        pool_columns = ('Pool', 'Status', 'Hashrate', 'Shares', 'Difficulty', 'Job ID')
        self.pool_stats_tree = ttk.Treeview(pool_stats_frame, columns=pool_columns, show='headings', height=8)
        
        for col in pool_columns:
            self.pool_stats_tree.heading(col, text=col)
            self.pool_stats_tree.column(col, width=120)
        
        self.pool_stats_tree.pack(fill=tk.BOTH, expand=True)
    
    def create_logs_tab(self):
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="Logs")
        
        # Log display
        log_frame = ttk.LabelFrame(logs_frame, text="Mining Logs", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, bg='#1e1e1e', fg='#ffffff', 
                                                 font=('Courier', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill=tk.X, pady=5)
        
        ttk.Button(log_controls, text="Clear Logs", command=self.clear_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_controls, text="Save Logs", command=self.save_logs).pack(side=tk.LEFT, padx=5)

    def on_pool_selected(self, event):
        """Handle predefined pool selection"""
        selected_pool = self.pool_var.get()
        for pool in self.predefined_pools:
            if pool['name'] == selected_pool:
                if pool['urls']:
                    self.pool_url_var.set(pool['urls'][0])
                break

    def add_pool(self):
        """Add a new mining pool"""
        pool_url = self.pool_url_var.get().strip()
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        worker = self.worker_var.get().strip()

        if not pool_url or not username:
            messagebox.showerror("Error", "Pool URL and Username are required!")
            return

        try:
            # Parse pool URL
            host = pool_url
            port = 3333  # default port

            if host.startswith('stratum+tcp://'):
                host = host.replace('stratum+tcp://', '')
            elif host.startswith('stratum+ssl://'):
                host = host.replace('stratum+ssl://', '')

            if ':' in host:
                host, port_str = host.split(':')
                port = int(port_str)

            # Add to mining engine
            if self.mining_engine.add_pool(host, port, username, password, worker):
                self.update_pools_display()
                self.log_message(f"Added pool: {host}:{port} with worker {worker}")

                # Clear form
                self.pool_url_var.set("")
                self.username_var.set("")
                self.password_var.set("x")
                self.worker_var.set(f"Whatsminer_M61s_{len(self.mining_engine.clients)+1:03d}")
            else:
                messagebox.showerror("Error", "Failed to add pool!")

        except Exception as e:
            messagebox.showerror("Error", f"Invalid pool configuration: {e}")

    def remove_pool(self):
        """Remove selected pool"""
        selection = self.pools_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a pool to remove!")
            return

        item = self.pools_tree.item(selection[0])
        pool_index = int(item['values'][0]) if item['values'] else -1

        if pool_index >= 0:
            if self.mining_engine.remove_pool(pool_index):
                self.update_pools_display()
                self.log_message(f"Removed pool at index {pool_index}")
            else:
                messagebox.showerror("Error", "Failed to remove pool!")

    def test_pool(self):
        """Test connection to selected pool"""
        selection = self.pools_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a pool to test!")
            return

        # This would implement a connection test
        messagebox.showinfo("Test", "Pool connection test feature coming soon!")

    def start_mining(self):
        """Start mining operation"""
        if not self.mining_engine.clients:
            messagebox.showerror("Error", "No pools configured! Please add at least one pool.")
            return

        if self.mining_engine.start_mining():
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.log_message("Mining started!")
        else:
            messagebox.showerror("Error", "Failed to start mining!")

    def stop_mining(self):
        """Stop mining operation"""
        self.mining_engine.stop_mining()
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.log_message("Mining stopped!")

    def update_pools_display(self):
        """Update the pools display"""
        # Clear existing items
        for item in self.pools_tree.get_children():
            self.pools_tree.delete(item)

        # Add current pools
        pool_stats = self.mining_engine.get_pool_stats()
        for i, pool in enumerate(pool_stats):
            status = "Connected" if pool['connected'] else "Disconnected"
            if pool['connected'] and pool['authorized']:
                status = "Mining"

            self.pools_tree.insert('', 'end', values=(
                i,
                pool['host'],
                pool['port'],
                pool['worker_name'],
                status
            ))

    def update_stats_display(self, stats=None):
        """Update statistics display"""
        if not stats:
            stats = self.mining_engine.get_total_stats()

        # Update overall stats
        self.stats_labels['total_hashrate'].config(text=self.mining_engine.format_hashrate(stats['total_hashrate']))
        self.stats_labels['connected_pools'].config(text=f"{stats['connected_pools']}/{stats['total_pools']}")
        self.stats_labels['shares_accepted'].config(text=str(stats['total_shares_accepted']))
        self.stats_labels['shares_rejected'].config(text=str(stats['total_shares_rejected']))
        self.stats_labels['uptime'].config(text=self.mining_engine.format_uptime(stats['uptime']))
        self.stats_labels['rejection_rate'].config(text=f"{stats['rejection_rate']:.2f}%")

        # Update pool stats
        for item in self.pool_stats_tree.get_children():
            self.pool_stats_tree.delete(item)

        pool_stats = self.mining_engine.get_pool_stats()
        for pool in pool_stats:
            status = "Disconnected"
            if pool['connected']:
                status = "Authorized" if pool['authorized'] else "Connected"

            hashrate_str = self.mining_engine.format_hashrate(pool['hashrate'])
            shares_str = f"{pool['shares_accepted']}/{pool['shares_rejected']}"

            self.pool_stats_tree.insert('', 'end', values=(
                f"{pool['host']}:{pool['port']}",
                status,
                hashrate_str,
                shares_str,
                pool['difficulty'],
                pool['job_id'] or 'None'
            ))

    def log_message(self, message):
        """Add message to status log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # Add to status text
        self.status_text.insert(tk.END, log_entry)
        self.status_text.see(tk.END)

        # Add to logs text
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

    def clear_logs(self):
        """Clear all logs"""
        self.log_text.delete(1.0, tk.END)

    def save_logs(self):
        """Save logs to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mining_logs_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))

            messagebox.showinfo("Success", f"Logs saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save logs: {e}")

    def start_gui_updates(self):
        """Start GUI update thread"""
        def update_loop():
            while True:
                try:
                    # Update displays every second
                    self.root.after(0, self.update_pools_display)
                    self.root.after(0, self.update_stats_display)
                    time.sleep(1)
                except:
                    break

        update_thread = threading.Thread(target=update_loop)
        update_thread.daemon = True
        update_thread.start()

def main():
    root = tk.Tk()
    app = BitcoinMinerGUI(root)

    # Handle window close
    def on_closing():
        app.mining_engine.stop_mining()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
